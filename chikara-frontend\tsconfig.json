{
    "$schema": "https://json.schemastore.org/tsconfig",
    "display": "Vite React",
    "_version": "3.0.0",

    "compilerOptions": {
        "allowJs": true,
        "strictNullChecks": true,
        "target": "ESNext",
        "useDefineForClassFields": true,
        "lib": ["ESNext", "DOM", "DOM.Iterable"],
        "module": "ESNext",
        "skipLibCheck": true,

        /* Bundler mode */
        "moduleResolution": "bundler",
        "allowImportingTsExtensions": true,
        "resolveJsonModule": true,
        "isolatedModules": true,
        "noEmit": true,
        "jsx": "react-jsx",

        "esModuleInterop": true,
        "forceConsistentCasingInFileNames": true,
        "removeComments": true,
        "typeRoots": ["./src/types"],
        "incremental": true,
        "tsBuildInfoFile": "./.tsbuildinfo",

        /* Performance optimizations */
        "disableSourceOfProjectReferenceRedirect": true,
        "disableSolutionSearching": true,
        "disableReferencedProjectLoad": true,

        /* Linting */
        "strict": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "noFallthroughCasesInSwitch": true,
        "noUncheckedSideEffectImports": true,
        "noImplicitAny": true,

        /* Paths */
        "baseUrl": ".",
        "paths": {
            "@/*": ["./src/*"],
            "~/*": ["./public/*"]
        },

        "types": ["vite/client"]
    },
    "references": [{ "path": "./tsconfig.node.json" }],
    "include": ["src", "vite-env.d.ts", "playwright/**/*"],
    "exclude": [
        "node_modules",
        "**/node_modules/**",
        "dist",
        "build",
        "coverage",
        "public",
        "service-worker",
        "**/.git/**",
        "**/tmp/**",
        "**/temp/**",
        "**/*.spec.ts",
        "**/*.test.ts",
        "**/test-results/**"
    ]
}
