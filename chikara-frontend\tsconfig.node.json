{
    "compilerOptions": {
        // Tells TS this is part of a larger project, enabling better tooling.
        "composite": true,
        "skipLibCheck": true,
        // Ensure that we can use modern ES Module syntax in our config files.
        "module": "ESNext",
        "moduleResolution": "bundler",
        "allowSyntheticDefaultImports": true,
        "strict": true
    },
    // Explicitly include only the config files.
    // This prevents mixing app code with Node.js environment code.
    "include": ["vite.config.ts"]
}
